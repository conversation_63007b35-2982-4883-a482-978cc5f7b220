#!/usr/bin/env tsx

/**
 * Script to enable marketplace settings for testing
 * Usage: npx tsx scripts/enable-marketplace.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function enableMarketplace() {
  try {
    console.log('🔧 Configuring marketplace settings...');
    
    const settingsToUpdate = [
      {
        key: 'market_enabled',
        value: 'true',
        description: 'Enable marketplace'
      },
      {
        key: 'market_paid_nodes',
        value: 'true',
        description: 'Enable paid nodes'
      },
      {
        key: 'market_free_nodes',
        value: 'true',
        description: 'Enable free nodes'
      },
      {
        key: 'market_approval',
        value: 'false',
        description: 'Disable node approval requirement for testing'
      },
      {
        key: 'market_uploads',
        value: 'true',
        description: 'Allow node uploads'
      }
    ];

    for (const setting of settingsToUpdate) {
      await prisma.appSetting.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          category: 'marketplace',
          field: setting.key.replace('market_', ''),
          value: setting.value,
          type: 'boolean',
          description: setting.description,
          isPublic: true
        }
      });
      
      console.log(`✅ ${setting.description}: ${setting.value}`);
    }

    console.log('\n🎉 Marketplace settings configured successfully!');
    console.log('📝 Settings applied:');
    console.log('  - Marketplace: Enabled');
    console.log('  - Paid Nodes: Enabled');
    console.log('  - Free Nodes: Enabled');
    console.log('  - Node Approval: Disabled (for testing)');
    console.log('  - Node Uploads: Enabled');
    
  } catch (error) {
    console.error('❌ Error configuring marketplace:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
enableMarketplace();
