#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to approve all pending nodes for testing purposes
 * Usage: npx tsx scripts/approve-nodes.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function approveAllNodes() {
  try {
    console.log('🔍 Finding pending nodes...');
    
    const pendingNodes = await prisma.nodePlugin.findMany({
      where: {
        verified: false
      },
      select: {
        id: true,
        name: true,
        version: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (pendingNodes.length === 0) {
      console.log('✅ No pending nodes found. All nodes are already approved.');
      return;
    }

    console.log(`📋 Found ${pendingNodes.length} pending nodes:`);
    pendingNodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} v${node.version} by ${node.author.name}`);
    });

    console.log('\n🚀 Approving all nodes...');
    
    const result = await prisma.nodePlugin.updateMany({
      where: {
        verified: false
      },
      data: {
        verified: true
      }
    });

    console.log(`✅ Successfully approved ${result.count} nodes!`);
    
    // Also make some nodes featured for testing
    const nodesToFeature = await prisma.nodePlugin.findMany({
      where: {
        verified: true,
        featured: false
      },
      take: 3,
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (nodesToFeature.length > 0) {
      console.log('\n⭐ Making some nodes featured...');
      
      await prisma.nodePlugin.updateMany({
        where: {
          id: {
            in: nodesToFeature.map(n => n.id)
          }
        },
        data: {
          featured: true
        }
      });

      console.log(`⭐ Made ${nodesToFeature.length} nodes featured!`);
    }

    console.log('\n🎉 All done! Nodes are now visible in the marketplace.');
    
  } catch (error) {
    console.error('❌ Error approving nodes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
approveAllNodes();
