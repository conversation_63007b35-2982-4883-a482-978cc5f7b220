/**
 * Hook for managing canvas nodes with real-time updates
 */

import { useState, useEffect, useCallback } from 'react';
import { useInstalledNodes } from './use-installed-nodes';
import { nodeRegistry } from '@/lib/workflow/node-registry';
import { NodeDefinition } from '@/lib/node-loader';
import { NodeMetadata } from '@/lib/workflow/node-interface';

export interface CanvasNode {
  id: string;
  type: string;
  name: string;
  description: string;
  category: string;
  icon: any;
  isInstalled: boolean;
  isBuiltIn: boolean;
  version?: string;
  definition?: NodeDefinition;
  metadata?: NodeMetadata;
}

export interface CanvasNodeCategory {
  id: string;
  title: string;
  nodes: CanvasNode[];
}

export function useCanvasNodes() {
  const [allNodes, setAllNodes] = useState<CanvasNode[]>([]);
  const [categories, setCategories] = useState<CanvasNodeCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    nodeDefinitions: installedNodes,
    loading: installedLoading,
    error: installedError,
    fetchInstalledNodes
  } = useInstalledNodes();

  // Combine built-in and installed nodes
  const refreshNodes = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get built-in nodes from registry
      const builtInNodes: CanvasNode[] = nodeRegistry.getAllNodes().map(metadata => ({
        id: metadata.type,
        type: metadata.type,
        name: metadata.label,
        description: metadata.description,
        category: metadata.category,
        icon: metadata.icon,
        isInstalled: true, // Built-in nodes are always "installed"
        isBuiltIn: true,
        metadata
      }));

      // Get installed marketplace nodes
      const marketplaceNodes: CanvasNode[] = installedNodes.map(node => ({
        id: node.id,
        type: node.type,
        name: node.name,
        description: node.description,
        category: node.category,
        icon: node.icon,
        isInstalled: true,
        isBuiltIn: false,
        version: node.version,
        definition: node
      }));

      // Combine all nodes
      const combined = [...builtInNodes, ...marketplaceNodes];
      setAllNodes(combined);

      // Group by category
      const categoryMap = new Map<string, CanvasNode[]>();
      
      combined.forEach(node => {
        const category = node.category;
        if (!categoryMap.has(category)) {
          categoryMap.set(category, []);
        }
        categoryMap.get(category)!.push(node);
      });

      // Convert to category array with proper titles
      const categoryInfo = nodeRegistry.getCategories();
      const categorizedNodes: CanvasNodeCategory[] = [];

      // Add built-in categories first
      Object.entries(categoryInfo).forEach(([categoryId, info]) => {
        const nodes = categoryMap.get(categoryId) || [];
        if (nodes.length > 0) {
          categorizedNodes.push({
            id: categoryId,
            title: info.title,
            nodes: nodes.sort((a, b) => a.name.localeCompare(b.name))
          });
        }
      });

      // Add any remaining categories from marketplace nodes
      categoryMap.forEach((nodes, categoryId) => {
        if (!categoryInfo[categoryId as keyof typeof categoryInfo]) {
          categorizedNodes.push({
            id: categoryId,
            title: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
            nodes: nodes.sort((a, b) => a.name.localeCompare(b.name))
          });
        }
      });

      setCategories(categorizedNodes);

    } catch (error) {
      console.error('Failed to refresh canvas nodes:', error);
      setError(error instanceof Error ? error.message : 'Failed to load nodes');
    } finally {
      setLoading(false);
    }
  }, [installedNodes]);

  // Refresh when installed nodes change
  useEffect(() => {
    if (!installedLoading) {
      refreshNodes();
    }
  }, [installedNodes, installedLoading, refreshNodes]);

  // Handle installation errors
  useEffect(() => {
    if (installedError) {
      setError(installedError);
    }
  }, [installedError]);

  const getNodesByCategory = useCallback((categoryId: string): CanvasNode[] => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.nodes || [];
  }, [categories]);

  const getNodeByType = useCallback((type: string): CanvasNode | null => {
    return allNodes.find(node => node.type === type) || null;
  }, [allNodes]);

  const getBuiltInNodes = useCallback((): CanvasNode[] => {
    return allNodes.filter(node => node.isBuiltIn);
  }, [allNodes]);

  const getInstalledMarketplaceNodes = useCallback((): CanvasNode[] => {
    return allNodes.filter(node => !node.isBuiltIn);
  }, [allNodes]);

  const searchNodes = useCallback((query: string): CanvasNode[] => {
    if (!query.trim()) return allNodes;
    
    const lowercaseQuery = query.toLowerCase();
    return allNodes.filter(node => 
      node.name.toLowerCase().includes(lowercaseQuery) ||
      node.description.toLowerCase().includes(lowercaseQuery) ||
      node.category.toLowerCase().includes(lowercaseQuery)
    );
  }, [allNodes]);

  const getNodeStats = useCallback(() => {
    const builtInCount = allNodes.filter(node => node.isBuiltIn).length;
    const installedCount = allNodes.filter(node => !node.isBuiltIn).length;
    
    return {
      total: allNodes.length,
      builtIn: builtInCount,
      installed: installedCount,
      categories: categories.length
    };
  }, [allNodes, categories]);

  // Listen for node installation events
  useEffect(() => {
    const handleNodeInstalled = () => {
      fetchInstalledNodes();
    };

    const handleNodeUninstalled = () => {
      fetchInstalledNodes();
    };

    // Listen for custom events (can be dispatched from installation system)
    window.addEventListener('nodeInstalled', handleNodeInstalled);
    window.addEventListener('nodeUninstalled', handleNodeUninstalled);

    return () => {
      window.removeEventListener('nodeInstalled', handleNodeInstalled);
      window.removeEventListener('nodeUninstalled', handleNodeUninstalled);
    };
  }, [fetchInstalledNodes]);

  return {
    // Data
    allNodes,
    categories,
    loading: loading || installedLoading,
    error,

    // Actions
    refreshNodes,
    fetchInstalledNodes,

    // Getters
    getNodesByCategory,
    getNodeByType,
    getBuiltInNodes,
    getInstalledMarketplaceNodes,
    searchNodes,
    getNodeStats,
  };
}

// Helper function to dispatch node events
export const dispatchNodeEvent = (eventType: 'nodeInstalled' | 'nodeUninstalled', nodeId: string) => {
  window.dispatchEvent(new CustomEvent(eventType, { detail: { nodeId } }));
};
