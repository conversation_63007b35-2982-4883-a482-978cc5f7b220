import { Setting<PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>ield } from './types';
import {
  CreditCard,
  Receipt,
  Crown,
  Store,
  Code,
  Workflow,
  Mail,
  Shield,
  Zap,
  Database,
  BarChart3,
  Settings,
  Palette,
  Flag
} from 'lucide-react';

export const settingCategories: SettingCategory[] = [
  {
    id: 'payments',
    name: 'Payment Gateway',
    description: 'Configure payment processing and gateway settings',
    icon: 'CreditCard',
    order: 1
  },
  {
    id: 'billing',
    name: 'Billing System',
    description: 'Manage billing, invoicing, and payment cycles',
    icon: 'Receipt',
    order: 2
  },
  {
    id: 'subscriptions',
    name: 'Subscriptions',
    description: 'Control subscription plans and user access',
    icon: 'Crown',
    order: 3
  },
  {
    id: 'marketplace',
    name: 'Marketplace',
    description: 'Configure node marketplace and paid content',
    icon: 'Store',
    order: 4
  },
  {
    id: 'developer',
    name: 'Developer Tools',
    description: 'Settings for node publishing and developer features',
    icon: 'Code',
    order: 5
  },
  {
    id: 'workflows',
    name: 'Workflow Engine',
    description: 'Configure workflow execution and limits',
    icon: 'Workflow',
    order: 6
  },
  {
    id: 'email',
    name: 'Email System',
    description: 'Email providers and notification settings',
    icon: 'Mail',
    order: 7
  },
  {
    id: 'security',
    name: 'Security',
    description: 'Authentication and security policies',
    icon: 'Shield',
    order: 8
  },
  {
    id: 'api',
    name: 'API Settings',
    description: 'Rate limiting and API configuration',
    icon: 'Zap',
    order: 9
  },
  {
    id: 'storage',
    name: 'Storage',
    description: 'File storage and backup settings',
    icon: 'Database',
    order: 10
  },
  {
    id: 'analytics',
    name: 'Analytics',
    description: 'Data tracking and analytics configuration',
    icon: 'BarChart3',
    order: 11
  },
  {
    id: 'maintenance',
    name: 'Maintenance',
    description: 'System maintenance and monitoring',
    icon: 'Settings',
    order: 12
  },
  {
    id: 'features',
    name: 'Feature Flags',
    description: 'Enable or disable experimental features',
    icon: 'Flag',
    order: 13
  },
  {
    id: 'ui',
    name: 'UI/UX',
    description: 'User interface and experience settings',
    icon: 'Palette',
    order: 14
  }
];

export const settingFields: Record<string, SettingField[]> = {
  payments: [
    {
      key: 'enabled',
      name: 'Enable Payments',
      description: 'Enable payment processing throughout the application',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'provider',
      name: 'Payment Provider',
      description: 'Select the payment gateway provider',
      type: 'select',
      defaultValue: 'disabled',
      options: [
        { label: 'Disabled', value: 'disabled' },
        { label: 'Doku', value: 'doku' }
      ],
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'dokuEnabled',
      name: 'Enable Doku',
      description: 'Enable Doku payment processing',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'testMode',
      name: 'Test Mode',
      description: 'Use test/sandbox mode for payments',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'currency',
      name: 'Default Currency',
      description: 'Default currency for transactions',
      type: 'select',
      defaultValue: 'idr',
      options: [
        { label: 'IDR', value: 'idr' },
        { label: 'USD', value: 'usd' },
        { label: 'EUR', value: 'eur' },
        { label: 'GBP', value: 'gbp' }
      ]
    },
    {
      key: 'commissionRate',
      name: 'Commission Rate',
      description: 'Platform commission rate (0-1)',
      type: 'number',
      defaultValue: 0.25,
      validation: { min: 0, max: 1 }
    },
    {
      key: 'dokuClientId',
      name: 'Doku Client ID',
      description: 'Doku Client ID from your Doku dashboard (e.g., MCH-0001-...)',
      type: 'string',
      defaultValue: '',
      placeholder: 'MCH-0001-...',
      validation: {
        required: true,
        pattern: /^MCH-\d{4}-/,
        patternMessage: 'Client ID should start with MCH-XXXX-'
      },
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuSecretKey',
      name: 'Doku Secret Key',
      description: 'Doku Secret Key from your Doku dashboard',
      type: 'password',
      defaultValue: '',
      placeholder: 'Enter your Doku secret key',
      validation: {
        required: true,
        minLength: 10
      },
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuEnvironment',
      name: 'Doku Environment',
      description: 'Select Doku environment (sandbox for testing)',
      type: 'select',
      defaultValue: 'sandbox',
      options: [
        { label: 'Sandbox (Testing)', value: 'sandbox' },
        { label: 'Production', value: 'production' }
      ],
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuNotificationUrl',
      name: 'Doku Notification URL',
      description: 'URL to receive payment notifications from Doku (webhook endpoint)',
      type: 'string',
      defaultValue: '',
      placeholder: 'https://yourdomain.com/api/payments/webhooks',
      dependencies: [{ field: 'provider', value: 'doku' }]
    }
  ],

  billing: [
    {
      key: 'enabled',
      name: 'Enable Billing',
      description: 'Enable the billing system',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'invoiceGeneration',
      name: 'Generate Invoices',
      description: 'Automatically generate invoices',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'automaticBilling',
      name: 'Automatic Billing',
      description: 'Enable automatic recurring billing',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'billingCycle',
      name: 'Billing Cycle',
      description: 'Default billing cycle',
      type: 'select',
      defaultValue: 'monthly',
      options: [
        { label: 'Monthly', value: 'monthly' },
        { label: 'Yearly', value: 'yearly' }
      ]
    },
    {
      key: 'gracePeriodDays',
      name: 'Grace Period (Days)',
      description: 'Grace period before service suspension',
      type: 'number',
      defaultValue: 7,
      validation: { min: 0, max: 30 }
    }
  ],

  subscriptions: [
    {
      key: 'enabled',
      name: 'Enable Subscriptions',
      description: 'Enable subscription system',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'allowFreePlan',
      name: 'Allow Free Plan',
      description: 'Allow users to use free plan',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowUpgrades',
      name: 'Allow Upgrades',
      description: 'Allow users to upgrade their plans',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'allowDowngrades',
      name: 'Allow Downgrades',
      description: 'Allow users to downgrade their plans',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'trialPeriodDays',
      name: 'Trial Period (Days)',
      description: 'Free trial period in days',
      type: 'number',
      defaultValue: 14,
      validation: { min: 0, max: 90 }
    }
  ],

  marketplace: [
    {
      key: 'enabled',
      name: 'Enable Marketplace',
      description: 'Enable the node marketplace',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'paidNodesEnabled',
      name: 'Enable Paid Nodes',
      description: 'Allow paid nodes in marketplace',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'freeNodesEnabled',
      name: 'Enable Free Nodes',
      description: 'Allow free nodes in marketplace',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'nodeApprovalRequired',
      name: 'Require Node Approval',
      description: 'Require admin approval for new nodes',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowNodeUploads',
      name: 'Allow Node Uploads',
      description: 'Allow developers to upload nodes',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'maxNodeSize',
      name: 'Max Node Size (MB)',
      description: 'Maximum size for uploaded nodes',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'reviewSystemEnabled',
      name: 'Enable Reviews',
      description: 'Enable node review system',
      type: 'boolean',
      defaultValue: true
    }
  ],

  developer: [
    {
      key: 'enabled',
      name: 'Enable Developer Tools',
      description: 'Enable developer features',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'nodePublishingEnabled',
      name: 'Enable Node Publishing',
      description: 'Allow developers to publish nodes',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'analyticsEnabled',
      name: 'Enable Analytics',
      description: 'Enable developer analytics',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'revenueShareEnabled',
      name: 'Enable Revenue Sharing',
      description: 'Enable revenue sharing for developers',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'maxNodesPerDeveloper',
      name: 'Max Nodes per Developer',
      description: 'Maximum nodes a developer can publish',
      type: 'number',
      defaultValue: 50,
      validation: { min: 1, max: 1000 }
    }
  ]
};

export function getSettingField(category: string, key: string): SettingField | undefined {
  return settingFields[category]?.find(field => field.key === key);
}

export function getCategoryIcon(category: string): any {
  const iconMap: Record<string, any> = {
    payments: CreditCard,
    billing: Receipt,
    subscriptions: Crown,
    marketplace: Store,
    developer: Code,
    workflows: Workflow,
    email: Mail,
    security: Shield,
    api: Zap,
    storage: Database,
    analytics: BarChart3,
    maintenance: Settings,
    features: Flag,
    ui: Palette
  };

  return iconMap[category] || Settings;
}
