/**
 * Doku Payment Processing Service
 * Handles all payment-related operations with Doku
 */

import {
  dokuConfig,
  getApiEndpoint,
  validatePaymentAmount,
  calculateCommission,
  subscriptionPlans
} from './config';
import { createRequestHeaders, validateResponseSignature } from './signature';
import {
  DokuCheckoutRequest,
  DokuCheckoutResponse,
  CreatePaymentIntentParams,
  CreateSubscriptionParams,
  CreateCheckoutSessionParams,
  PaymentStatus,
  DokuNotification
} from './types';
import { prisma } from '@/lib/prisma';

export class PaymentService {
  /**
   * Create a checkout session for node purchase
   */
  async createCheckoutSession(params: CreatePaymentIntentParams): Promise<DokuCheckoutResponse> {
    const { nodeId, userId, amount, currency = dokuConfig.currency, metadata = {} } = params;

    // Validate payment amount
    const validation = validatePaymentAmount(amount);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Get node details
    const node = await prisma.nodePlugin.findUnique({
      where: { id: nodeId },
      include: { author: true }
    });

    if (!node) {
      throw new Error('Node not found');
    }

    // Check if user already owns the node
    const existingPurchase = await prisma.nodePurchase.findFirst({
      where: {
        nodeId,
        userId,
        status: 'completed'
      }
    });

    if (existingPurchase) {
      throw new Error('Node already purchased');
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Generate unique invoice number
    const invoiceNumber = `NODE-${Date.now()}-${nodeId.substring(0, 8)}`;

    // Create checkout request
    const checkoutRequest: DokuCheckoutRequest = {
      order: {
        amount,
        invoice_number: invoiceNumber,
        currency,
        callback_url: `${process.env.NEXTAUTH_URL}/api/payments/success`,
        callback_url_cancel: `${process.env.NEXTAUTH_URL}/api/payments/cancel`,
        auto_redirect: true,
        line_items: [{
          id: nodeId,
          name: node.name,
          quantity: 1,
          price: amount,
          sku: nodeId,
          category: node.category || 'workflow-node',
          url: `${process.env.NEXTAUTH_URL}/marketplace/node/${nodeId}`,
          type: 'digital'
        }]
      },
      payment: {
        payment_due_date: dokuConfig.defaultPaymentDueDate
      },
      customer: {
        id: userId,
        name: user.name || 'User',
        email: user.email || '',
        phone: user.phone || '',
        country: 'ID'
      }
    };

    // Make API request to Doku
    const response = await this.makeApiRequest(checkoutRequest);

    // Store payment intent in database
    await prisma.payment.create({
      data: {
        id: response.response.headers.request_id,
        userId,
        amount,
        currency,
        status: 'pending',
        paymentMethod: 'doku_checkout',
        invoiceNumber,
        dokuTokenId: response.response.payment.token_id,
        dokuSessionId: response.response.order.session_id,
        metadata: {
          nodeId,
          nodeName: node.name,
          ...metadata
        }
      }
    });

    return response;
  }

  /**
   * Create subscription checkout session
   */
  async createSubscriptionCheckout(params: CreateCheckoutSessionParams): Promise<DokuCheckoutResponse> {
    const { userId, planId, successUrl, cancelUrl, trialDays = 0 } = params;

    // Get subscription plan
    const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Generate unique invoice number
    const invoiceNumber = `SUB-${Date.now()}-${planId}`;

    // Create checkout request
    const checkoutRequest: DokuCheckoutRequest = {
      order: {
        amount: plan.price,
        invoice_number: invoiceNumber,
        currency: dokuConfig.currency,
        callback_url: successUrl,
        callback_url_cancel: cancelUrl,
        auto_redirect: true,
        line_items: [{
          id: planId,
          name: `${plan.name} Subscription`,
          quantity: 1,
          price: plan.price,
          sku: planId,
          category: 'subscription',
          type: 'subscription'
        }]
      },
      payment: {
        payment_due_date: dokuConfig.defaultPaymentDueDate
      },
      customer: {
        id: userId,
        name: user.name || 'User',
        email: user.email || '',
        phone: user.phone || '',
        country: 'ID'
      }
    };

    // Make API request to Doku
    const response = await this.makeApiRequest(checkoutRequest);

    // Store subscription payment intent in database
    await prisma.payment.create({
      data: {
        id: response.response.headers.request_id,
        userId,
        amount: plan.price,
        currency: dokuConfig.currency,
        status: 'pending',
        paymentMethod: 'doku_checkout',
        invoiceNumber,
        dokuTokenId: response.response.payment.token_id,
        dokuSessionId: response.response.order.session_id,
        metadata: {
          planId,
          planName: plan.name,
          trialDays,
          type: 'subscription'
        }
      }
    });

    return response;
  }

  /**
   * Handle payment notification from Doku
   */
  async handleNotification(notification: DokuNotification): Promise<void> {
    const { order, transaction } = notification;

    // Find payment record
    const payment = await prisma.payment.findFirst({
      where: {
        invoiceNumber: order.invoice_number
      }
    });

    if (!payment) {
      throw new Error('Payment not found');
    }

    // Update payment status
    const status = this.mapDokuStatusToPaymentStatus(transaction.status);

    await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status,
        dokuTransactionId: transaction.id,
        dokuReferenceNumber: transaction.reference_number,
        updatedAt: new Date()
      }
    });

    // Handle successful payment
    if (status === 'completed') {
      await this.handleSuccessfulPayment(payment);
    }
  }

  /**
   * Handle successful payment completion
   */
  private async handleSuccessfulPayment(payment: any): Promise<void> {
    const metadata = payment.metadata as any;

    if (metadata.type === 'subscription') {
      // Handle subscription activation
      await this.activateSubscription(payment.userId, metadata.planId);
    } else if (metadata.nodeId) {
      // Handle node purchase
      await this.completeNodePurchase(payment.userId, metadata.nodeId, payment);
    }
  }

  /**
   * Activate subscription for user
   */
  private async activateSubscription(userId: string, planId: string): Promise<void> {
    const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    // Calculate subscription end date
    const startDate = new Date();
    const endDate = new Date();
    if (plan.interval === 'month') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    // Create or update subscription
    await prisma.subscription.upsert({
      where: { userId },
      update: {
        planId,
        status: 'active',
        currentPeriodStart: startDate,
        currentPeriodEnd: endDate,
        updatedAt: new Date()
      },
      create: {
        userId,
        planId,
        status: 'active',
        currentPeriodStart: startDate,
        currentPeriodEnd: endDate
      }
    });
  }

  /**
   * Complete node purchase
   */
  private async completeNodePurchase(userId: string, nodeId: string, payment: any): Promise<void> {
    // Create node purchase record
    await prisma.nodePurchase.create({
      data: {
        userId,
        nodeId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'completed',
        transactionId: payment.dokuTransactionId,
        purchasedAt: new Date()
      }
    });

    // Calculate and record commission
    const commission = calculateCommission(payment.amount);
    const authorAmount = payment.amount - commission;

    // Record revenue for node author
    const node = await prisma.nodePlugin.findUnique({
      where: { id: nodeId }
    });

    if (node) {
      await prisma.revenue.create({
        data: {
          userId: node.authorId,
          nodeId,
          amount: authorAmount,
          commission,
          transactionId: payment.dokuTransactionId,
          type: 'node_sale'
        }
      });
    }
  }

  /**
   * Make API request to Doku
   */
  private async makeApiRequest(requestData: DokuCheckoutRequest): Promise<DokuCheckoutResponse> {
    const requestBody = JSON.stringify(requestData);
    const headers = createRequestHeaders(requestBody);
    const apiEndpoint = getApiEndpoint();

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers,
      body: requestBody
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Doku API error: ${response.status} - ${errorText}`);
    }

    const responseData: DokuCheckoutResponse = await response.json();

    // Validate response signature
    const responseHeaders = response.headers;
    const isValidSignature = validateResponseSignature(
      headers['Client-Id'],
      headers['Request-Id'],
      responseHeaders.get('Response-Timestamp') || '',
      JSON.stringify(responseData),
      responseHeaders.get('Signature') || ''
    );

    if (!isValidSignature) {
      console.warn('Invalid response signature from Doku');
    }

    return responseData;
  }

  /**
   * Map Doku transaction status to our payment status
   */
  private mapDokuStatusToPaymentStatus(dokuStatus: string): PaymentStatus {
    switch (dokuStatus) {
      case 'SUCCESS':
        return 'completed';
      case 'FAILED':
        return 'failed';
      case 'PENDING':
        return 'pending';
      case 'EXPIRED':
        return 'expired';
      default:
        return 'pending';
    }
  }

  /**
   * Get payment history for user
   */
  async getPaymentHistory(userId: string, limit: number = 10, offset: number = 0) {
    return await prisma.payment.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
  }

  /**
   * Get subscription for user
   */
  async getUserSubscription(userId: string) {
    return await prisma.subscription.findUnique({
      where: { userId }
    });
  }

  /**
   * Create subscription (for paid plans)
   */
  async createSubscription(params: CreateSubscriptionParams) {
    const { userId, planId, paymentMethodId, trialDays = 0 } = params;

    // Get subscription plan
    const plan = subscriptionPlans[planId as keyof typeof subscriptionPlans];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    // For Doku, we'll create a checkout session for subscription
    const checkoutSession = await this.createSubscriptionCheckout({
      userId,
      planId,
      successUrl: `${process.env.NEXTAUTH_URL}/profile?checkout=success`,
      cancelUrl: `${process.env.NEXTAUTH_URL}/marketplace?tab=subscription&checkout=cancelled`,
      trialDays
    });

    // Return a subscription-like object
    return {
      id: checkoutSession.response.order.session_id,
      status: 'pending',
      current_period_start: Math.floor(Date.now() / 1000),
      current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000), // 30 days
      trial_start: trialDays > 0 ? Math.floor(Date.now() / 1000) : null,
      trial_end: trialDays > 0 ? Math.floor((Date.now() + trialDays * 24 * 60 * 60 * 1000) / 1000) : null,
      cancel_at_period_end: false
    };
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(params: { subscriptionId: string; immediately: boolean; cancellationReason: string }) {
    const { subscriptionId, immediately, cancellationReason } = params;

    // Find subscription in database
    const subscription = await prisma.subscription.findFirst({
      where: { dokuSubscriptionId: subscriptionId }
    });

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // Update subscription status
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: immediately ? 'canceled' : 'active',
        cancelAtPeriodEnd: !immediately,
        canceledAt: immediately ? new Date() : null,
        metadata: {
          ...subscription.metadata,
          cancellationReason,
          canceledAt: new Date().toISOString()
        }
      }
    });

    return {
      id: subscriptionId,
      status: immediately ? 'canceled' : 'active',
      cancel_at_period_end: !immediately
    };
  }

  /**
   * Update subscription
   */
  async updateSubscription(params: { subscriptionId: string; newPlanId: string; prorationBehavior?: string }) {
    const { subscriptionId, newPlanId } = params;

    // Find subscription in database
    const subscription = await prisma.subscription.findFirst({
      where: { dokuSubscriptionId: subscriptionId }
    });

    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // Get new plan
    const newPlan = subscriptionPlans[newPlanId as keyof typeof subscriptionPlans];
    if (!newPlan) {
      throw new Error('Invalid plan ID');
    }

    // For Doku, we'll need to create a new checkout session for the plan change
    // and update the subscription record
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        planId: newPlanId,
        updatedAt: new Date(),
        metadata: {
          ...subscription.metadata,
          previousPlanId: subscription.planId,
          updatedAt: new Date().toISOString()
        }
      }
    });

    return {
      id: subscriptionId,
      status: 'active'
    };
  }
}
