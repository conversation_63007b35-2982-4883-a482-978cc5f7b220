/**
 * Enhanced Node Installation System with Progress Tracking
 */

export interface InstallationProgress {
  nodeId: string;
  status: 'pending' | 'downloading' | 'validating' | 'installing' | 'configuring' | 'completed' | 'failed';
  progress: number; // 0-100
  message: string;
  error?: string;
  startTime: Date;
  endTime?: Date;
}

export interface InstallationOptions {
  version?: string;
  force?: boolean; // Force reinstall
  skipValidation?: boolean;
  onProgress?: (progress: InstallationProgress) => void;
}

export interface DependencyInfo {
  nodeId: string;
  version: string;
  required: boolean;
  installed: boolean;
}

export class NodeInstallationManager {
  private static instance: NodeInstallationManager;
  private activeInstallations = new Map<string, InstallationProgress>();
  private progressCallbacks = new Map<string, ((progress: InstallationProgress) => void)[]>();

  static getInstance(): NodeInstallationManager {
    if (!NodeInstallationManager.instance) {
      NodeInstallationManager.instance = new NodeInstallationManager();
    }
    return NodeInstallationManager.instance;
  }

  /**
   * Install a node with progress tracking
   */
  async installNode(nodeId: string, options: InstallationOptions = {}): Promise<boolean> {
    const progress: InstallationProgress = {
      nodeId,
      status: 'pending',
      progress: 0,
      message: 'Preparing installation...',
      startTime: new Date()
    };

    this.activeInstallations.set(nodeId, progress);
    this.notifyProgress(nodeId, progress);

    try {
      // Step 1: Check dependencies
      await this.updateProgress(nodeId, {
        status: 'downloading',
        progress: 10,
        message: 'Checking dependencies...'
      });

      const dependencies = await this.checkDependencies(nodeId, options.version);
      
      // Step 2: Download node package
      await this.updateProgress(nodeId, {
        status: 'downloading',
        progress: 30,
        message: 'Downloading node package...'
      });

      const downloadResult = await this.downloadNode(nodeId, options.version);
      if (!downloadResult.success) {
        throw new Error(downloadResult.error || 'Download failed');
      }

      // Step 3: Validate package
      if (!options.skipValidation) {
        await this.updateProgress(nodeId, {
          status: 'validating',
          progress: 50,
          message: 'Validating package integrity...'
        });

        const isValid = await this.validatePackage(nodeId, downloadResult.packageData);
        if (!isValid) {
          throw new Error('Package validation failed');
        }
      }

      // Step 4: Install dependencies
      await this.updateProgress(nodeId, {
        status: 'installing',
        progress: 70,
        message: 'Installing dependencies...'
      });

      await this.installDependencies(dependencies);

      // Step 5: Register node
      await this.updateProgress(nodeId, {
        status: 'configuring',
        progress: 90,
        message: 'Configuring node...'
      });

      const installResult = await this.registerNode(nodeId, downloadResult.packageData, options);
      if (!installResult.success) {
        throw new Error(installResult.error || 'Registration failed');
      }

      // Step 6: Complete
      await this.updateProgress(nodeId, {
        status: 'completed',
        progress: 100,
        message: 'Installation completed successfully!',
        endTime: new Date()
      });

      return true;

    } catch (error) {
      await this.updateProgress(nodeId, {
        status: 'failed',
        progress: 0,
        message: 'Installation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        endTime: new Date()
      });

      // Cleanup on failure
      await this.cleanupFailedInstallation(nodeId);
      return false;
    } finally {
      // Clean up after 30 seconds
      setTimeout(() => {
        this.activeInstallations.delete(nodeId);
        this.progressCallbacks.delete(nodeId);
      }, 30000);
    }
  }

  /**
   * Uninstall a node with progress tracking
   */
  async uninstallNode(nodeId: string): Promise<boolean> {
    const progress: InstallationProgress = {
      nodeId,
      status: 'pending',
      progress: 0,
      message: 'Preparing uninstallation...',
      startTime: new Date()
    };

    this.activeInstallations.set(nodeId, progress);
    this.notifyProgress(nodeId, progress);

    try {
      // Step 1: Check dependencies
      await this.updateProgress(nodeId, {
        status: 'downloading', // Reusing status for uninstall steps
        progress: 20,
        message: 'Checking for dependent nodes...'
      });

      const dependents = await this.checkDependentNodes(nodeId);
      if (dependents.length > 0) {
        throw new Error(`Cannot uninstall: ${dependents.length} nodes depend on this node`);
      }

      // Step 2: Stop running instances
      await this.updateProgress(nodeId, {
        status: 'configuring',
        progress: 40,
        message: 'Stopping running instances...'
      });

      await this.stopNodeInstances(nodeId);

      // Step 3: Remove from workflows
      await this.updateProgress(nodeId, {
        status: 'configuring',
        progress: 60,
        message: 'Removing from workflows...'
      });

      await this.removeFromWorkflows(nodeId);

      // Step 4: Unregister and cleanup
      await this.updateProgress(nodeId, {
        status: 'configuring',
        progress: 80,
        message: 'Cleaning up files...'
      });

      const uninstallResult = await this.unregisterNode(nodeId);
      if (!uninstallResult.success) {
        throw new Error(uninstallResult.error || 'Unregistration failed');
      }

      // Step 5: Complete
      await this.updateProgress(nodeId, {
        status: 'completed',
        progress: 100,
        message: 'Uninstallation completed successfully!',
        endTime: new Date()
      });

      return true;

    } catch (error) {
      await this.updateProgress(nodeId, {
        status: 'failed',
        progress: 0,
        message: 'Uninstallation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        endTime: new Date()
      });

      return false;
    }
  }

  /**
   * Subscribe to installation progress
   */
  onProgress(nodeId: string, callback: (progress: InstallationProgress) => void): () => void {
    if (!this.progressCallbacks.has(nodeId)) {
      this.progressCallbacks.set(nodeId, []);
    }
    
    this.progressCallbacks.get(nodeId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.progressCallbacks.get(nodeId);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Get current installation progress
   */
  getProgress(nodeId: string): InstallationProgress | null {
    return this.activeInstallations.get(nodeId) || null;
  }

  /**
   * Get all active installations
   */
  getActiveInstallations(): InstallationProgress[] {
    return Array.from(this.activeInstallations.values());
  }

  private async updateProgress(nodeId: string, updates: Partial<InstallationProgress>): Promise<void> {
    const current = this.activeInstallations.get(nodeId);
    if (!current) return;

    const updated = { ...current, ...updates };
    this.activeInstallations.set(nodeId, updated);
    this.notifyProgress(nodeId, updated);

    // Add small delay for visual feedback
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private notifyProgress(nodeId: string, progress: InstallationProgress): void {
    const callbacks = this.progressCallbacks.get(nodeId) || [];
    callbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('Error in progress callback:', error);
      }
    });
  }

  private async checkDependencies(nodeId: string, version?: string): Promise<DependencyInfo[]> {
    const response = await fetch(`/api/nodes/${nodeId}/dependencies${version ? `?version=${version}` : ''}`);
    if (!response.ok) {
      throw new Error('Failed to check dependencies');
    }
    return response.json();
  }

  private async downloadNode(nodeId: string, version?: string): Promise<{ success: boolean; packageData?: any; error?: string }> {
    const response = await fetch('/api/nodes/install', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ nodeId, version, downloadOnly: true })
    });

    if (!response.ok) {
      const error = await response.json();
      return { success: false, error: error.error };
    }

    const data = await response.json();
    return { success: true, packageData: data };
  }

  private async validatePackage(nodeId: string, packageData: any): Promise<boolean> {
    // Implement package validation logic
    // Check checksums, signatures, etc.
    return true; // Simplified for now
  }

  private async installDependencies(dependencies: DependencyInfo[]): Promise<void> {
    for (const dep of dependencies) {
      if (!dep.installed && dep.required) {
        // Install dependency recursively
        await this.installNode(dep.nodeId, { version: dep.version });
      }
    }
  }

  private async registerNode(nodeId: string, packageData: any, options: InstallationOptions): Promise<{ success: boolean; error?: string }> {
    const response = await fetch('/api/nodes/install', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        nodeId, 
        version: options.version,
        force: options.force,
        packageData 
      })
    });

    if (!response.ok) {
      const error = await response.json();
      return { success: false, error: error.error };
    }

    return { success: true };
  }

  private async cleanupFailedInstallation(nodeId: string): Promise<void> {
    try {
      await fetch(`/api/nodes/cleanup/${nodeId}`, { method: 'POST' });
    } catch (error) {
      console.error('Failed to cleanup installation:', error);
    }
  }

  private async checkDependentNodes(nodeId: string): Promise<string[]> {
    const response = await fetch(`/api/nodes/${nodeId}/dependents`);
    if (!response.ok) return [];
    
    const data = await response.json();
    return data.dependents || [];
  }

  private async stopNodeInstances(nodeId: string): Promise<void> {
    await fetch(`/api/nodes/${nodeId}/stop`, { method: 'POST' });
  }

  private async removeFromWorkflows(nodeId: string): Promise<void> {
    await fetch(`/api/nodes/${nodeId}/remove-from-workflows`, { method: 'POST' });
  }

  private async unregisterNode(nodeId: string): Promise<{ success: boolean; error?: string }> {
    const response = await fetch('/api/nodes/uninstall', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ nodeId })
    });

    if (!response.ok) {
      const error = await response.json();
      return { success: false, error: error.error };
    }

    return { success: true };
  }
}
