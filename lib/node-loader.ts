// Node Loader for Dynamic Plugin System
import { NodeProps } from 'reactflow';

export interface NodeDefinition {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  icon: string;
  version: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
  config: NodeConfig[];
  component: React.ComponentType<NodeProps>;
  execute?: (inputs: any, config: any) => Promise<any>;
}

export interface NodeInput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

export interface NodeOutput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  description?: string;
}

export interface NodeConfig {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'password';
  required: boolean;
  description?: string;
  defaultValue?: any;
  options?: { label: string; value: any }[];
}

export class NodeLoader {
  private static instance: NodeLoader;
  private loadedNodes: Map<string, NodeDefinition> = new Map();
  private nodeCache: Map<string, string> = new Map();

  static getInstance(): NodeLoader {
    if (!NodeLoader.instance) {
      NodeLoader.instance = new NodeLoader();
    }
    return NodeLoader.instance;
  }

  async loadNode(nodeId: string, version?: string): Promise<NodeDefinition | null> {
    try {
      // Check if node is already loaded
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      if (this.loadedNodes.has(cacheKey)) {
        return this.loadedNodes.get(cacheKey)!;
      }

      // Fetch node code from API
      const response = await fetch(`/api/nodes/code/${nodeId}${version ? `?version=${version}` : ''}`);
      if (!response.ok) {
        throw new Error(`Failed to load node: ${response.statusText}`);
      }

      const { nodeCode } = await response.json();
      
      // Create a secure execution environment
      const nodeDefinition = await this.executeNodeCode(nodeCode);
      
      // Cache the loaded node
      this.loadedNodes.set(cacheKey, nodeDefinition);
      
      return nodeDefinition;
    } catch (error) {
      console.error(`Failed to load node ${nodeId}:`, error);
      return null;
    }
  }

  private async executeNodeCode(nodeCode: any): Promise<NodeDefinition> {
    return new Promise((resolve, reject) => {
      try {
        // Create a Web Worker for secure execution
        const workerCode = `
          // Import necessary dependencies
          ${nodeCode.code}
          
          // Send the node definition back to main thread
          if (typeof nodeDefinition !== 'undefined') {
            self.postMessage({ type: 'definition', data: nodeDefinition });
          } else {
            self.postMessage({ type: 'error', error: 'nodeDefinition not found' });
          }
        `;

        const blob = new Blob([workerCode], { type: 'application/javascript' });
        const worker = new Worker(URL.createObjectURL(blob));

        const timeout = setTimeout(() => {
          worker.terminate();
          reject(new Error('Node loading timeout'));
        }, 5000);

        worker.onmessage = (event) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blob.toString());

          if (event.data.type === 'definition') {
            resolve(event.data.data);
          } else {
            reject(new Error(event.data.error || 'Unknown error'));
          }
        };

        worker.onerror = (error) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blob.toString());
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  async getInstalledNodes(): Promise<NodeDefinition[]> {
    try {
      const response = await fetch('/api/nodes/installed');
      if (!response.ok) {
        throw new Error('Failed to fetch installed nodes');
      }

      const { installedNodes } = await response.json();
      const nodeDefinitions: NodeDefinition[] = [];

      for (const installation of installedNodes) {
        if (installation.enabled && installation.status === 'installed') {
          const nodeDefinition = await this.loadNode(installation.nodeId, installation.version);
          if (nodeDefinition) {
            nodeDefinitions.push(nodeDefinition);
          }
        }
      }

      return nodeDefinitions;
    } catch (error) {
      console.error('Failed to get installed nodes:', error);
      return [];
    }
  }

  async installNode(nodeId: string, version?: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/install', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, version }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Installation failed');
      }

      // Clear cache to force reload
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      this.loadedNodes.delete(cacheKey);

      return true;
    } catch (error) {
      console.error(`Failed to install node ${nodeId}:`, error);
      return false;
    }
  }

  async uninstallNode(nodeId: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/uninstall', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Uninstallation failed');
      }

      // Remove from cache
      for (const [key] of this.loadedNodes) {
        if (key.startsWith(`${nodeId}:`)) {
          this.loadedNodes.delete(key);
        }
      }

      return true;
    } catch (error) {
      console.error(`Failed to uninstall node ${nodeId}:`, error);
      return false;
    }
  }

  getLoadedNode(nodeId: string, version?: string): NodeDefinition | null {
    const cacheKey = `${nodeId}:${version || 'latest'}`;
    return this.loadedNodes.get(cacheKey) || null;
  }

  clearCache(): void {
    this.loadedNodes.clear();
    this.nodeCache.clear();
  }
}

// Export singleton instance
export const nodeLoader = NodeLoader.getInstance();
