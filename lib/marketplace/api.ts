/**
 * Marketplace API Service
 * Handles all API calls to the marketplace backend
 */

import {
  NodePlugin,
  MarketplaceFilters,
  NodeReview,
  PaymentIntent,
  NodeSubscription,
  DeveloperMetrics,
  InstallationStatus
} from './types';

export class MarketplaceAPI {
  private baseUrl: string;
  private apiKey?: string;

  constructor(baseUrl: string = '/api/marketplace', apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API Error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  // Node Discovery & Search
  async searchNodes(filters: MarketplaceFilters = {}): Promise<{
    nodes: NodePlugin[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params.append(key, JSON.stringify(value));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    return this.request(`/nodes?${params.toString()}`);
  }

  async getNode(nodeId: string): Promise<NodePlugin> {
    return this.request(`/nodes/${nodeId}`);
  }

  async getFeaturedNodes(): Promise<NodePlugin[]> {
    return this.request('/nodes/featured');
  }

  async getPopularNodes(limit: number = 10): Promise<NodePlugin[]> {
    return this.request(`/nodes/popular?limit=${limit}`);
  }

  async getNewNodes(limit: number = 10): Promise<NodePlugin[]> {
    return this.request(`/nodes/new?limit=${limit}`);
  }

  // Node Installation & Management
  async downloadNode(nodeId: string): Promise<{
    downloadUrl: string;
    checksum: string;
    size: number;
  }> {
    return this.request(`/nodes/${nodeId}/download`, {
      method: 'POST'
    });
  }

  async reportInstallation(nodeId: string, status: InstallationStatus): Promise<void> {
    await this.request(`/nodes/${nodeId}/install`, {
      method: 'POST',
      body: JSON.stringify({ status })
    });
  }

  async reportUsage(nodeId: string, workflowId: string): Promise<void> {
    await this.request(`/nodes/${nodeId}/usage`, {
      method: 'POST',
      body: JSON.stringify({ workflowId, timestamp: new Date() })
    });
  }

  // Reviews & Ratings
  async getNodeReviews(nodeId: string, page: number = 1): Promise<{
    reviews: NodeReview[];
    total: number;
    averageRating: number;
  }> {
    return this.request(`/nodes/${nodeId}/reviews?page=${page}`);
  }

  async submitReview(nodeId: string, review: {
    rating: number;
    title: string;
    comment: string;
  }): Promise<NodeReview> {
    return this.request(`/nodes/${nodeId}/reviews`, {
      method: 'POST',
      body: JSON.stringify(review)
    });
  }

  async markReviewHelpful(reviewId: string): Promise<void> {
    await this.request(`/reviews/${reviewId}/helpful`, {
      method: 'POST'
    });
  }

  // Payment & Subscriptions
  async createPaymentIntent(nodeId: string): Promise<PaymentIntent> {
    return this.request('/payments/intent', {
      method: 'POST',
      body: JSON.stringify({ nodeId })
    });
  }

  async confirmPayment(paymentIntentId: string): Promise<{
    success: boolean;
    nodeId: string;
  }> {
    return this.request(`/payments/${paymentIntentId}/confirm`, {
      method: 'POST'
    });
  }

  async getSubscriptions(): Promise<NodeSubscription[]> {
    return this.request('/subscriptions');
  }

  async createSubscription(planId: string): Promise<NodeSubscription> {
    return this.request('/subscriptions', {
      method: 'POST',
      body: JSON.stringify({ planId })
    });
  }

  async cancelSubscription(subscriptionId: string): Promise<void> {
    await this.request(`/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST'
    });
  }

  // User Library
  async getUserNodes(): Promise<NodePlugin[]> {
    return this.request('/user/nodes');
  }

  async getUserPurchases(): Promise<{
    nodes: NodePlugin[];
    subscriptions: NodeSubscription[];
  }> {
    return this.request('/user/purchases');
  }

  // Developer API
  async publishNode(nodeData: FormData): Promise<NodePlugin> {
    return this.request('/developer/nodes', {
      method: 'POST',
      body: nodeData,
      headers: {} // Let browser set Content-Type for FormData
    });
  }

  async updateNode(nodeId: string, nodeData: FormData): Promise<NodePlugin> {
    return this.request(`/developer/nodes/${nodeId}`, {
      method: 'PUT',
      body: nodeData,
      headers: {} // Let browser set Content-Type for FormData
    });
  }

  async getDeveloperMetrics(developerId?: string): Promise<DeveloperMetrics> {
    const endpoint = developerId
      ? `/developer/metrics/${developerId}`
      : '/developer/metrics';
    return this.request(endpoint);
  }

  async getDeveloperNodes(): Promise<NodePlugin[]> {
    return this.request('/developer/nodes');
  }

  // Categories & Tags
  async getCategories(): Promise<{
    category: string;
    count: number;
    featured: boolean;
  }[]> {
    return this.request('/categories');
  }

  async getPopularTags(): Promise<{
    tag: string;
    count: number;
  }[]> {
    return this.request('/tags/popular');
  }

  // Analytics
  async getMarketplaceStats(): Promise<{
    totalNodes: number;
    totalDownloads: number;
    totalDevelopers: number;
    categoriesCount: Record<string, number>;
    recentActivity: {
      date: string;
      downloads: number;
      newNodes: number;
    }[];
  }> {
    return this.request('/stats');
  }
}

// Export singleton instance
export const marketplaceAPI = new MarketplaceAPI();

// Helper functions for common operations
export const marketplaceHelpers = {
  async installNode(nodeId: string): Promise<boolean> {
    try {
      await marketplaceAPI.reportInstallation(nodeId, InstallationStatus.INSTALLING);

      // Download node
      const downloadInfo = await marketplaceAPI.downloadNode(nodeId);

      // TODO: Implement actual installation logic
      // This would involve downloading, validating, and registering the node

      await marketplaceAPI.reportInstallation(nodeId, InstallationStatus.INSTALLED);
      return true;
    } catch (error) {
      await marketplaceAPI.reportInstallation(nodeId, InstallationStatus.ERROR);
      console.error('Failed to install node:', error);
      return false;
    }
  },

  async purchaseNode(nodeId: string): Promise<boolean> {
    try {
      const paymentIntent = await marketplaceAPI.createPaymentIntent(nodeId);

      // TODO: Integrate with Stripe or other payment provider
      // This would show payment UI and handle the payment flow

      const result = await marketplaceAPI.confirmPayment(paymentIntent.id);
      return result.success;
    } catch (error) {
      console.error('Failed to purchase node:', error);
      return false;
    }
  },

  formatPrice(price: number, currency: string = 'IDR'): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency
    }).format(price);
  },

  formatDownloads(downloads: number): string {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  }
};
