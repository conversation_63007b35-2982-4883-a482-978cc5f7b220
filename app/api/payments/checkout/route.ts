/**
 * Doku Checkout API Route
 * Creates checkout sessions for subscription upgrades
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PaymentService } from '@/lib/doku/payment-service';
import { subscriptionPlans, validateDokuConfig } from '@/lib/doku/config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Validate Doku configuration
    const configValidation = validateDokuConfig();
    if (!configValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Payment system not configured',
          details: configValidation.errors
        },
        { status: 500 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();
    const { planId, trialDays = 0 } = body;

    // Validate plan ID
    if (!planId || !subscriptionPlans[planId as keyof typeof subscriptionPlans]) {
      return NextResponse.json(
        { error: 'Invalid plan ID' },
        { status: 400 }
      );
    }

    // Check if it's a free plan
    if (planId === 'free') {
      return NextResponse.json(
        { error: 'Cannot create checkout session for free plan' },
        { status: 400 }
      );
    }

    // Get base URL for redirect URLs
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const successUrl = `${baseUrl}/profile?checkout=success`;
    const cancelUrl = `${baseUrl}/marketplace?tab=subscription&checkout=cancelled`;

    // Create checkout session
    const paymentService = new PaymentService();
    const checkoutSession = await paymentService.createSubscriptionCheckout({
      userId: user.id,
      planId,
      successUrl,
      cancelUrl,
      trialDays
    });

    return NextResponse.json({
      checkoutUrl: checkoutSession.response.payment.url,
      tokenId: checkoutSession.response.payment.token_id,
      sessionId: checkoutSession.response.order.session_id,
      expiredDate: checkoutSession.response.payment.expired_date
    });

  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
