/**
 * Payment History API Route
 * Handles fetching user's payment history and purchase records
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PaymentService } from '@/lib/doku/payment-service';
import { formatPrice } from '@/lib/doku/config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status'); // pending, completed, failed, refunded

    // Get payment history
    const paymentService = new PaymentService();
    const purchases = await paymentService.getPaymentHistory(user.id, limit, offset);

    // Filter by status if provided
    let filteredPurchases = purchases;
    if (status) {
      filteredPurchases = purchases.filter(purchase => purchase.status === status);
    }

    // Apply pagination
    const paginatedPurchases = filteredPurchases.slice(offset, offset + limit);

    // Format response
    const formattedPurchases = paginatedPurchases.map(purchase => ({
      id: purchase.id,
      nodeId: purchase.nodeId,
      nodeName: purchase.node?.name || 'Unknown Node',
      nodeVersion: purchase.node?.version || '1.0.0',
      nodeIcon: purchase.node?.icon || '',
      amount: purchase.amount,
      formattedAmount: formatPrice(purchase.amount / 100, purchase.currency),
      commission: purchase.commission,
      netAmount: purchase.netAmount,
      currency: purchase.currency,
      status: purchase.status,
      paymentIntentId: purchase.paymentIntentId,
      completedAt: purchase.completedAt,
      refundedAt: purchase.refundedAt,
      refundAmount: purchase.refundAmount,
      refundReason: purchase.refundReason,
      createdAt: purchase.createdAt,
      metadata: purchase.metadata ? JSON.parse(purchase.metadata) : null
    }));

    return NextResponse.json({
      purchases: formattedPurchases,
      pagination: {
        total: filteredPurchases.length,
        limit,
        offset,
        hasMore: offset + limit < filteredPurchases.length
      }
    });

  } catch (error: any) {
    console.error('Error fetching payment history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment history' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
