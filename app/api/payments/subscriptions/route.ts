/**
 * Subscription Management API Route
 * Handles subscription creation, updates, and cancellation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { PaymentService } from '@/lib/doku/payment-service';
import { subscriptionPlans, validateDokuConfig } from '@/lib/doku/config';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication first
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user has any local subscription first (including free plans)
    const localSubscription = await prisma.subscription.findFirst({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' }
    });

    if (localSubscription) {
      return NextResponse.json({
        subscription: {
          id: localSubscription.id,
          status: localSubscription.status,
          planId: localSubscription.planId,
          currentPeriodStart: localSubscription.currentPeriodStart.toISOString(),
          currentPeriodEnd: localSubscription.currentPeriodEnd.toISOString(),
          cancelAtPeriodEnd: localSubscription.cancelAtPeriodEnd,
          trialStart: null,
          trialEnd: null
        }
      });
    }

    // If no local subscription and Stripe is not configured, return null
    const configValidation = validateStripeConfig();
    if (!configValidation.isValid) {
      return NextResponse.json({ subscription: null });
    }

    // Try to fetch from Stripe if configured
    const paymentService = new PaymentService();
    const subscription = await paymentService.getSubscription(user.id);

    return NextResponse.json({ subscription });

  } catch (error: any) {
    console.error('Error fetching subscription:', error);

    // If it's a Stripe configuration error, return null subscription instead of error
    if (error.message?.includes('Stripe') || error.message?.includes('not configured')) {
      return NextResponse.json({ subscription: null });
    }

    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body first to check if it's a free plan
    const body = await request.json();
    const { planId, paymentMethodId, trialDays = 0 } = body;

    // For free plan, skip Doku validation
    const isFreeplan = planId === 'free';

    if (!isFreeplan) {
      // Validate Doku configuration only for paid plans
      const configValidation = validateDokuConfig();
      if (!configValidation.isValid) {
        return NextResponse.json(
          {
            error: 'Payment system not configured',
            details: configValidation.errors
          },
          { status: 500 }
        );
      }
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    let user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate required fields
    if (!planId) {
      return NextResponse.json(
        { error: 'Missing required field: planId' },
        { status: 400 }
      );
    }

    // Validate plan exists
    const plan = Object.values(subscriptionPlans).find(p => p.id === planId);
    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid plan ID' },
        { status: 400 }
      );
    }

    // Handle free plan separately (no PaymentService needed)
    if (planId === 'free' || plan.price === 0) {
      // User already exists from the authentication check above
      // No need to re-fetch the user

      if (!user) {
        // Create user record if it doesn't exist (OAuth users)
        try {
          // Generate a unique username
          const baseUsername = session.user.email?.split('@')[0] || 'user';
          const uniqueId = Math.random().toString(36).substring(2, 10);
          const username = `${baseUsername}_${uniqueId}`;

          const newUser = await prisma.user.create({
            data: {
              email: session.user.email || '',
              username: username,
              name: session.user.name || '',
              avatar: session.user.image || null,
              emailVerified: true, // OAuth users are considered verified
            }
          });
          user = newUser;
          console.log('Created new user:', newUser.id);
        } catch (error: any) {
          // If user creation fails due to unique constraint, try to find existing user again
          if (error.code === 'P2002') {
            console.log('User already exists due to unique constraint, fetching existing user...');

            // Try to find the user by email
            if (session.user.email) {
              user = await prisma.user.findUnique({
                where: { email: session.user.email }
              });
            }

            if (!user) {
              console.error('Failed to find existing user after unique constraint error');
              throw new Error('User creation failed due to unique constraint and existing user not found');
            }

            console.log('Found existing user:', user.id);
          } else {
            console.error('User creation failed with unexpected error:', error);
            throw error;
          }
        }
      }

      // Create a local subscription record for free plan
      const actualUserId = user?.id;
      if (!actualUserId) {
        throw new Error('User ID not available');
      }
      const freeSubscription = await prisma.subscription.create({
        data: {
          id: `free_${actualUserId}_${Date.now()}`,
          userId: actualUserId,
          dokuSubscriptionId: `free_${actualUserId}_${Date.now()}`, // Unique ID for free plan
          dokuCustomerId: '',
          status: 'active',
          planId,
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          cancelAtPeriodEnd: false,
          metadata: JSON.stringify({
            planId,
            type: 'free'
          })
        }
      });

      // Return a subscription-like object for consistency
      return NextResponse.json({
        subscription: {
          id: freeSubscription.id,
          status: 'active',
          planId: freeSubscription.planId,
          currentPeriodStart: Math.floor(freeSubscription.currentPeriodStart.getTime() / 1000),
          currentPeriodEnd: Math.floor(freeSubscription.currentPeriodEnd.getTime() / 1000),
          cancelAtPeriodEnd: false,
          trialStart: null,
          trialEnd: null
        }
      });
    }

    // For paid plans, create PaymentService and subscription
    const paymentService = new PaymentService();
    const subscription = await paymentService.createSubscription({
      userId: user.id,
      planId,
      paymentMethodId,
      trialDays
    });

    return NextResponse.json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        planId,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
        trialStart: subscription.trial_start,
        trialEnd: subscription.trial_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    });

  } catch (error: any) {
    console.error('Error creating subscription:', error);

    // Handle specific error types
    if (error.message === 'User not found') {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (error.message === 'Invalid plan ID') {
      return NextResponse.json(
        { error: 'Invalid plan ID' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const subscriptionId = searchParams.get('subscriptionId');
    const immediately = searchParams.get('immediately') === 'true';
    const cancellationReason = searchParams.get('reason') || 'User requested';

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Missing subscriptionId parameter' },
        { status: 400 }
      );
    }

    // Verify subscription belongs to user
    const subscription = await prisma.subscription.findFirst({
      where: {
        dokuSubscriptionId: subscriptionId,
        userId: user.id
      }
    });

    if (!subscription) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    // Handle free plan cancellation
    if (subscription.planId === 'free') {
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status: 'canceled',
          cancelAtPeriodEnd: false,
          canceledAt: new Date(),
          metadata: JSON.stringify({
            cancellationReason,
            canceledAt: new Date().toISOString()
          })
        }
      });

      return NextResponse.json({
        subscription: {
          id: subscription.id,
          status: 'canceled',
          message: 'Free plan subscription canceled successfully'
        }
      });
    }

    // Handle paid plan cancellation
    const configValidation = validateDokuConfig();
    if (!configValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Payment system not configured',
          details: configValidation.errors
        },
        { status: 500 }
      );
    }

    const paymentService = new PaymentService();
    const canceledSubscription = await paymentService.cancelSubscription({
      subscriptionId,
      immediately,
      cancellationReason
    });

    return NextResponse.json({
      subscription: {
        id: canceledSubscription.id,
        status: canceledSubscription.status,
        cancelAtPeriodEnd: canceledSubscription.cancel_at_period_end,
        message: immediately
          ? 'Subscription canceled immediately'
          : 'Subscription will be canceled at the end of the current billing period'
      }
    });

  } catch (error: any) {
    console.error('Error canceling subscription:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
