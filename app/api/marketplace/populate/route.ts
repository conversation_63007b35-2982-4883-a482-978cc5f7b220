/**
 * API endpoint to populate marketplace with sample nodes
 * POST /api/marketplace/populate
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const sampleNodes = [
  {
    id: 'enterprise-salesforce',
    name: 'Enterprise Salesforce Connector',
    version: '2.1.0',
    description: 'Advanced Salesforce integration with CRM automation, lead management, and custom object support.',
    longDescription: 'A comprehensive Salesforce connector that enables seamless integration with your Salesforce CRM. Features include automated lead scoring, opportunity tracking, custom object manipulation, and advanced reporting capabilities.',
    category: 'integration',
    tier: 'premium',
    price: 299000, // 299,000 IDR
    subscriptionType: 'one_time',
    tags: '["salesforce", "crm", "enterprise", "integration", "automation"]',
    dependencies: '["@salesforce/core", "jsforce"]',
    permissions: '["network", "storage"]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/salesforce/salesforce-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/0176D3/FFFFFF?text=Salesforce+Dashboard"]',
    repositoryUrl: 'https://github.com/example/salesforce-connector',
    documentationUrl: 'https://docs.example.com/salesforce-connector',
    verified: true,
    featured: true,
    rating: 4.8,
    downloads: 15420,
    weeklyDownloads: 342,
    compatibility: '{"node": ">=16.0.0", "workflow": ">=2.0.0"}',
    changelog: '{"2.1.0": "Added custom object support", "2.0.0": "Major performance improvements"}'
  },
  {
    id: 'ai-text-analyzer',
    name: 'AI Text Analyzer Pro',
    version: '1.5.2',
    description: 'Advanced AI-powered text analysis with sentiment detection, entity extraction, and language processing.',
    longDescription: 'Leverage cutting-edge AI to analyze text content with high accuracy. Features include sentiment analysis, named entity recognition, language detection, keyword extraction, and content classification.',
    category: 'ai',
    tier: 'premium',
    price: 199000, // 199,000 IDR
    subscriptionType: 'one_time',
    tags: '["ai", "nlp", "sentiment", "analysis", "machine-learning"]',
    dependencies: '["@tensorflow/tfjs", "natural"]',
    permissions: '["network"]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tensorflow/tensorflow-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/FF6F00/FFFFFF?text=AI+Analysis"]',
    repositoryUrl: 'https://github.com/example/ai-text-analyzer',
    documentationUrl: 'https://docs.example.com/ai-text-analyzer',
    verified: true,
    featured: true,
    rating: 4.6,
    downloads: 8930,
    weeklyDownloads: 156,
    compatibility: '{"node": ">=18.0.0", "workflow": ">=2.1.0"}',
    changelog: '{"1.5.2": "Improved accuracy", "1.5.0": "Added multi-language support"}'
  },
  {
    id: 'database-sync-pro',
    name: 'Database Sync Pro',
    version: '3.0.1',
    description: 'Professional database synchronization tool supporting MySQL, PostgreSQL, MongoDB, and more.',
    longDescription: 'A powerful database synchronization solution that enables real-time data sync between multiple database systems. Supports incremental sync, conflict resolution, schema migration, and data transformation.',
    category: 'database',
    tier: 'premium',
    price: 399000, // 399,000 IDR
    subscriptionType: 'one_time',
    tags: '["database", "sync", "mysql", "postgresql", "mongodb"]',
    dependencies: '["mysql2", "pg", "mongodb"]',
    permissions: '["network", "storage"]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/4479A1/FFFFFF?text=Database+Sync"]',
    repositoryUrl: 'https://github.com/example/database-sync-pro',
    documentationUrl: 'https://docs.example.com/database-sync-pro',
    verified: true,
    featured: false,
    rating: 4.7,
    downloads: 5670,
    weeklyDownloads: 89,
    compatibility: '{"node": ">=16.0.0", "workflow": ">=2.0.0"}',
    changelog: '{"3.0.1": "Bug fixes", "3.0.0": "Added MongoDB support"}'
  },
  {
    id: 'email-automation',
    name: 'Email Automation Suite',
    version: '2.3.0',
    description: 'Complete email automation solution with templates, scheduling, and analytics.',
    longDescription: 'A comprehensive email automation platform that handles everything from template design to delivery analytics. Features include drag-and-drop template builder, advanced scheduling, A/B testing, bounce handling, and detailed performance metrics.',
    category: 'communication',
    tier: 'premium',
    price: 149000, // 149,000 IDR
    subscriptionType: 'one_time',
    tags: '["email", "automation", "marketing", "templates", "analytics"]',
    dependencies: '["nodemailer", "mjml"]',
    permissions: '["network"]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/68217A/FFFFFF?text=Email+Templates"]',
    repositoryUrl: 'https://github.com/example/email-automation',
    documentationUrl: 'https://docs.example.com/email-automation',
    verified: true,
    featured: false,
    rating: 4.4,
    downloads: 12340,
    weeklyDownloads: 234,
    compatibility: '{"node": ">=16.0.0", "workflow": ">=2.0.0"}',
    changelog: '{"2.3.0": "Added A/B testing", "2.2.0": "Improved template editor"}'
  },
  {
    id: 'web-scraper-advanced',
    name: 'Advanced Web Scraper',
    version: '1.8.5',
    description: 'Professional web scraping tool with JavaScript rendering, proxy support, and anti-detection.',
    longDescription: 'A sophisticated web scraping solution that handles modern websites with JavaScript rendering, CAPTCHA solving, proxy rotation, and advanced anti-detection measures.',
    category: 'data',
    tier: 'premium',
    price: 249000, // 249,000 IDR
    subscriptionType: 'one_time',
    tags: '["scraping", "data-extraction", "puppeteer", "proxy", "automation"]',
    dependencies: '["puppeteer", "cheerio", "axios"]',
    permissions: '["network", "storage"]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/chrome/chrome-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/4285F4/FFFFFF?text=Web+Scraping"]',
    repositoryUrl: 'https://github.com/example/web-scraper-advanced',
    documentationUrl: 'https://docs.example.com/web-scraper-advanced',
    verified: true,
    featured: true,
    rating: 4.5,
    downloads: 9876,
    weeklyDownloads: 187,
    compatibility: '{"node": ">=18.0.0", "workflow": ">=2.1.0"}',
    changelog: '{"1.8.5": "Improved anti-detection", "1.8.0": "Added CAPTCHA solving"}'
  },
  {
    id: 'simple-calculator',
    name: 'Simple Calculator',
    version: '1.0.0',
    description: 'Basic calculator for mathematical operations in workflows.',
    longDescription: 'A simple yet effective calculator node for performing basic mathematical operations within your workflows. Supports addition, subtraction, multiplication, division, and basic functions.',
    category: 'utility',
    tier: 'free',
    price: null,
    subscriptionType: null,
    tags: '["calculator", "math", "utility", "basic"]',
    dependencies: '[]',
    permissions: '[]',
    icon: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg',
    screenshots: '["https://via.placeholder.com/800x600/F7DF1E/000000?text=Calculator"]',
    repositoryUrl: 'https://github.com/example/simple-calculator',
    documentationUrl: 'https://docs.example.com/simple-calculator',
    verified: true,
    featured: false,
    rating: 4.2,
    downloads: 25670,
    weeklyDownloads: 456,
    compatibility: '{"node": ">=14.0.0", "workflow": ">=1.0.0"}',
    changelog: '{"1.0.0": "Initial release"}'
  }
];

export async function POST(request: NextRequest) {
  try {
    // Get or create default user
    let defaultUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!defaultUser) {
      defaultUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Marketplace Admin',
          username: 'marketplace-admin',
          avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=Marketplace%20Admin'
        }
      });
    }

    // Clear existing nodes (optional)
    await prisma.nodePlugin.deleteMany({});

    // Insert sample nodes
    const createdNodes = [];
    for (const nodeData of sampleNodes) {
      const node = await prisma.nodePlugin.create({
        data: {
          ...nodeData,
          authorId: defaultUser.id,
          downloadUrl: `/api/marketplace/download/${nodeData.id}.zip`,
          lastUpdated: new Date(),
          createdAt: new Date()
        }
      });
      createdNodes.push(node);
    }

    return NextResponse.json({
      success: true,
      message: 'Marketplace populated successfully',
      nodesCreated: createdNodes.length,
      nodes: createdNodes.map(n => ({ id: n.id, name: n.name, price: n.price }))
    });

  } catch (error: any) {
    console.error('Error populating marketplace:', error);
    return NextResponse.json(
      { error: 'Failed to populate marketplace', details: error.message },
      { status: 500 }
    );
  }
}
