/**
 * Individual Node API Route
 * Handles fetching details for a specific marketplace node
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get node details with author information
    const node = await prisma.nodePlugin.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            purchases: true
          }
        }
      }
    });

    if (!node) {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404 }
      );
    }

    // Format the response
    const formattedNode = {
      id: node.id,
      name: node.name,
      version: node.version,
      description: node.description,
      longDescription: node.longDescription,
      category: node.category,
      tier: node.tier,
      price: node.price,
      subscriptionType: node.subscriptionType,
      tags: node.tags,
      dependencies: node.dependencies,
      permissions: node.permissions,
      icon: node.icon,
      screenshots: node.screenshots,
      downloadUrl: node.downloadUrl,
      repositoryUrl: node.repositoryUrl,
      documentationUrl: node.documentationUrl,
      verified: node.verified,
      featured: node.featured,
      rating: node.rating,
      reviewCount: node._count.reviews,
      downloads: node.downloads,
      weeklyDownloads: node.weeklyDownloads,
      lastUpdated: node.lastUpdated,
      createdAt: node.createdAt,
      compatibility: node.compatibility,
      changelog: node.changelog,
      author: {
        id: node.author.id,
        name: node.author.name || 'Unknown Author',
        email: node.author.email,
        avatar: node.author.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(node.author.name || 'Unknown')}`
      },
      purchaseCount: node._count.purchases
    };

    return NextResponse.json({ node: formattedNode });

  } catch (error: any) {
    console.error('Error fetching node details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch node details' },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
