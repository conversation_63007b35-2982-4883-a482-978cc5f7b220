import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST() {
  try {
    console.log('🔧 Enabling marketplace settings...');
    
    const settingsToUpdate = [
      {
        key: 'market_enabled',
        category: 'marketplace',
        field: 'enabled',
        value: 'true',
        type: 'boolean',
        description: 'Enable marketplace',
        isPublic: true
      },
      {
        key: 'market_paid_nodes',
        category: 'marketplace',
        field: 'paidNodesEnabled',
        value: 'true',
        type: 'boolean',
        description: 'Enable paid nodes',
        isPublic: true
      },
      {
        key: 'market_free_nodes',
        category: 'marketplace',
        field: 'freeNodesEnabled',
        value: 'true',
        type: 'boolean',
        description: 'Enable free nodes',
        isPublic: true
      },
      {
        key: 'market_approval',
        category: 'marketplace',
        field: 'nodeApprovalRequired',
        value: 'false',
        type: 'boolean',
        description: 'Require node approval',
        isPublic: true
      },
      {
        key: 'market_uploads',
        category: 'marketplace',
        field: 'allowNodeUploads',
        value: 'true',
        type: 'boolean',
        description: 'Allow node uploads',
        isPublic: true
      }
    ];

    const results = [];
    
    for (const setting of settingsToUpdate) {
      const result = await prisma.appSetting.upsert({
        where: { key: setting.key },
        update: { 
          value: setting.value,
          updatedAt: new Date()
        },
        create: setting
      });
      
      results.push({
        key: setting.key,
        description: setting.description,
        value: setting.value,
        updated: true
      });
      
      console.log(`✅ ${setting.description}: ${setting.value}`);
    }

    // Also approve all existing nodes for testing
    const nodeUpdateResult = await prisma.nodePlugin.updateMany({
      where: {
        verified: false
      },
      data: {
        verified: true
      }
    });

    console.log(`✅ Approved ${nodeUpdateResult.count} nodes`);

    return NextResponse.json({
      success: true,
      message: 'Marketplace settings enabled successfully',
      settings: results,
      nodesApproved: nodeUpdateResult.count
    });
    
  } catch (error) {
    console.error('❌ Error enabling marketplace:', error);
    return NextResponse.json({ 
      error: 'Failed to enable marketplace',
      details: error.message 
    }, { status: 500 });
  }
}
