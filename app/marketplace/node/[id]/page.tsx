"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import {
  Star,
  Download,
  ShoppingCart,
  ExternalLink,
  ArrowLeft,
  Calendar,
  Users,
  Shield,
  Package,
} from "lucide-react";
import { NodePlugin, NodeTier } from "@/lib/marketplace/types";
import { marketplaceAPI, marketplaceHelpers } from "@/lib/marketplace/api";
import Link from "next/link";

export default function NodeDetailPage() {
  const params = useParams();
  const nodeId = params?.id as string;
  const [node, setNode] = useState<NodePlugin | null>(null);
  const [loading, setLoading] = useState(true);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    if (nodeId) {
      loadNodeDetails();
    }
  }, [nodeId]);

  const loadNodeDetails = async () => {
    try {
      setLoading(true);

      // Fetch node details from API
      const response = await fetch(`/api/marketplace/nodes/${nodeId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch node details');
      }

      const data = await response.json();
      setNode(data.node || null);
    } catch (error) {
      console.error('Failed to load node details:', error);
      setNode(null);
    } finally {
      setLoading(false);
    }
  };

  const handleInstall = async () => {
    if (!node) return;

    setIsInstalling(true);
    try {
      console.log("Installing node:", node.id);
      // Simulate installation
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("Node installed successfully");
    } catch (error) {
      console.error("Installation failed:", error);
    } finally {
      setIsInstalling(false);
    }
  };

  const StarRating = ({ rating }: { rating: number }) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-muted-foreground'
            }`}
          />
        ))}
        <span className="text-sm text-muted-foreground ml-2">
          {rating.toFixed(1)} ({node?.reviewCount} reviews)
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!node) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Node not found</h2>
              <p className="text-muted-foreground mb-4">The requested node could not be found.</p>
              <Link href="/marketplace">
                <Button>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Marketplace
                </Button>
              </Link>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/marketplace">Marketplace</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{node.name}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-6 p-6">
          {/* Header Section */}
          <div className="flex items-start gap-6">
            <img
              src={node.icon}
              alt={node.name}
              className="w-20 h-20 rounded-xl object-cover border"
            />
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold">{node.name}</h1>
                <Badge variant={node.tier === NodeTier.FREE ? 'secondary' : 'default'}>
                  {node.tier}
                </Badge>
                {node.featured && (
                  <Badge variant="outline" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                    Featured
                  </Badge>
                )}
                {node.verified && (
                  <Badge variant="outline">
                    <Shield className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-4 mb-3">
                <div className="flex items-center gap-2">
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={node.author.avatar} />
                    <AvatarFallback className="text-xs">
                      {node.author.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-muted-foreground">by {node.author.name}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Download className="h-4 w-4" />
                  {marketplaceHelpers.formatDownloads(node.downloads)} downloads
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Package className="h-4 w-4" />
                  v{node.version}
                </div>
              </div>

              <StarRating rating={node.rating} />
            </div>

            <div className="flex gap-3">
              {node.repositoryUrl && (
                <Button variant="outline" onClick={() => window.open(node.repositoryUrl, '_blank')}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Repository
                </Button>
              )}
              <Button
                onClick={handleInstall}
                disabled={isInstalling}
                className="min-w-[120px]"
              >
                {isInstalling ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Installing...
                  </>
                ) : (
                  <>
                    {node.tier === NodeTier.FREE ? (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Install
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        {node.price ? marketplaceHelpers.formatPrice(node.price, 'IDR') : 'Purchase'}
                      </>
                    )}
                  </>
                )}
              </Button>
            </div>
          </div>

          <Separator />

          {/* Description */}
          <div>
            <h2 className="text-xl font-semibold mb-3">Description</h2>
            <p className="text-muted-foreground leading-relaxed">
              {node.longDescription || node.description}
            </p>
          </div>

          {/* Screenshots */}
          {node.screenshots.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-3">Screenshots</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {node.screenshots.map((screenshot, index) => (
                  <img
                    key={index}
                    src={screenshot}
                    alt={`${node.name} screenshot ${index + 1}`}
                    className="w-full h-48 object-cover rounded-lg border"
                  />
                ))}
              </div>
            </div>
          )}

          {/* Tags */}
          <div>
            <h2 className="text-xl font-semibold mb-3">Tags</h2>
            <div className="flex flex-wrap gap-2">
              {node.tags.map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
