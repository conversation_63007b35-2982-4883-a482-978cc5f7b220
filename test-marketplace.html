<!DOCTYPE html>
<html>
<head>
    <title>Marketplace Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Marketplace Settings Test</h1>
    
    <div class="section">
        <h2>1. Enable Marketplace Settings</h2>
        <button onclick="enableMarketplace()">Enable Marketplace</button>
        <div id="enableResult"></div>
    </div>

    <div class="section">
        <h2>2. Check Current Settings</h2>
        <button onclick="checkSettings()">Check Settings</button>
        <div id="settingsResult"></div>
    </div>

    <div class="section">
        <h2>3. Check Marketplace Nodes</h2>
        <button onclick="checkNodes()">Check Nodes</button>
        <div id="nodesResult"></div>
    </div>

    <div class="section">
        <h2>4. Create Test Node</h2>
        <button onclick="createTestNode()">Create Test Node</button>
        <div id="createResult"></div>
    </div>

    <script>
        async function enableMarketplace() {
            const resultDiv = document.getElementById('enableResult');
            resultDiv.innerHTML = 'Enabling marketplace...';
            
            try {
                const response = await fetch('/api/settings', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: 'marketplace',
                        settings: {
                            enabled: true,
                            paidNodesEnabled: true,
                            freeNodesEnabled: true,
                            nodeApprovalRequired: false,
                            allowNodeUploads: true,
                            maxNodeSize: 10,
                            allowedFileTypes: ['.js', '.ts', '.json'],
                            featuredNodesEnabled: true,
                            reviewSystemEnabled: true,
                            ratingSystemEnabled: true
                        }
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Marketplace enabled successfully!</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        async function checkSettings() {
            const resultDiv = document.getElementById('settingsResult');
            resultDiv.innerHTML = 'Checking settings...';
            
            try {
                const response = await fetch('/api/debug/marketplace');
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Settings retrieved</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        async function checkNodes() {
            const resultDiv = document.getElementById('nodesResult');
            resultDiv.innerHTML = 'Checking marketplace nodes...';
            
            try {
                const response = await fetch('/api/marketplace/nodes');
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Found ${result.nodes.length} nodes</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        async function createTestNode() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = 'Creating test node...';
            
            try {
                // Create a simple test node via the API
                const formData = new FormData();
                formData.append('name', 'Test Node from Browser');
                formData.append('version', '1.0.0');
                formData.append('description', 'A test node created from the browser test page');
                formData.append('longDescription', 'This is a detailed description of the test node created for testing marketplace functionality.');
                formData.append('category', 'utility');
                formData.append('tier', 'free');
                formData.append('price', '0');
                formData.append('tags', JSON.stringify(['test', 'browser', 'utility']));
                formData.append('repositoryUrl', 'https://github.com/test/test-node');
                formData.append('documentationUrl', 'https://docs.test.com');
                
                // Create a dummy zip file
                const zipContent = 'PK\x03\x04\x14\x00\x00\x00\x08\x00'; // ZIP file header
                const zipBlob = new Blob([zipContent], { type: 'application/zip' });
                formData.append('package', zipBlob, 'test-node.zip');

                const response = await fetch('/api/developer/nodes', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Test node created successfully!</div><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
