// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id                String     @id @default(uuid())
  email             String     @unique
  username          String     @unique
  password          String?    // Make password optional for OAuth users
  name              String?
  bio               String?
  avatar            String?
  emailVerified     Boolean    @default(false)
  verificationToken String?
  resetToken        String?
  resetTokenExpiry  DateTime?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  // Doku integration
  dokuCustomerId    String?    @unique

  workflows         Workflow[]
  accounts          Account[]
  sessions          Session[]

  // Marketplace relations
  publishedNodes    NodePlugin[]
  nodeReviews       NodeReview[]
  nodePurchases     NodePurchase[]
  nodeSubscriptions NodeSubscription[]
  nodeUsage         NodeUsage[]
  userLibrary       UserLibrary[]
  subscriptions     Subscription[]

  // Plug & Play System relations
  installedNodes    InstalledNode[]
  nodeExecutionLogs NodeExecutionLog[]
  workflowExecutions WorkflowExecution[]

  // Settings relations
  createdSettings   AppSettings[] @relation("SettingsCreatedBy")
  updatedSettings   AppSettings[] @relation("SettingsUpdatedBy")

  // Payment relations
  payments          Payment[]
  revenues          Revenue[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Workflow {
  id          String   @id @default(uuid())
  name        String
  description String?
  nodes       String   // JSON string of nodes
  edges       String   // JSON string of edges
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Plug & Play System relations
  nodeExecutionLogs NodeExecutionLog[]
  executions        WorkflowExecution[]

  @@index([userId])
}

// Marketplace Models
model NodePlugin {
  id              String   @id @default(uuid())
  name            String
  version         String
  description     String
  longDescription String?
  authorId        String
  category        String
  tier            String   // free, premium, enterprise, subscription
  price           Float?
  subscriptionType String? // monthly, yearly
  tags            String   // JSON array
  dependencies    String   // JSON array
  permissions     String   // JSON array
  icon            String
  screenshots     String   // JSON array
  downloadUrl     String
  repositoryUrl   String?
  documentationUrl String?
  verified        Boolean  @default(false)
  featured        Boolean  @default(false)
  rating          Float    @default(0)
  reviewCount     Int      @default(0)
  downloads       Int      @default(0)
  weeklyDownloads Int      @default(0)
  lastUpdated     DateTime @updatedAt
  createdAt       DateTime @default(now())
  compatibility   String   // JSON object
  changelog       String?  // JSON array

  // Relations
  author          User           @relation(fields: [authorId], references: [id], onDelete: Cascade)
  reviews         NodeReview[]
  purchases       NodePurchase[]
  usage           NodeUsage[]
  userLibrary     UserLibrary[]

  // Plug & Play System relations
  installedNodes  InstalledNode[]
  nodeCodes       NodeCode[]
  revenues        Revenue[]

  @@index([authorId])
  @@index([category])
  @@index([tier])
  @@index([featured])
  @@index([verified])
}

model NodeReview {
  id         String   @id @default(uuid())
  nodeId     String
  userId     String
  rating     Int
  title      String
  comment    String
  helpful    Int      @default(0)
  verified   Boolean  @default(false)
  createdAt  DateTime @default(now())

  // Relations
  node       NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([nodeId, userId])
  @@index([nodeId])
  @@index([userId])
}

model NodePurchase {
  id              String    @id @default(uuid())
  nodeId          String
  userId          String
  amount          Float
  commission      Float     @default(0)
  netAmount       Float     @default(0)
  currency        String    @default("USD")
  paymentIntentId String?   @unique
  status          String    // pending, completed, failed, refunded
  completedAt     DateTime?
  refundedAt      DateTime?
  refundAmount    Float?
  refundReason    String?
  metadata        String?   // JSON
  createdAt       DateTime  @default(now())

  // Relations
  node            NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)
  user            User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([nodeId])
  @@index([userId])
  @@index([status])
  @@index([paymentIntentId])
}

model NodeSubscription {
  id                  String   @id @default(uuid())
  userId              String
  planId              String
  nodeIds             String   // JSON array
  status              String   // active, canceled, past_due, unpaid
  currentPeriodStart  DateTime
  currentPeriodEnd    DateTime
  cancelAtPeriodEnd   Boolean  @default(false)
  dokuSubscriptionId  String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model NodeUsage {
  id         String   @id @default(uuid())
  nodeId     String
  userId     String
  workflowId String?
  timestamp  DateTime @default(now())
  duration   Int?     // milliseconds
  success    Boolean  @default(true)
  error      String?

  // Relations
  node       NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([nodeId])
  @@index([userId])
  @@index([timestamp])
}

// User's installed nodes library
model UserLibrary {
  id          String   @id @default(uuid())
  userId      String
  nodeId      String
  installedAt DateTime @default(now())
  enabled     Boolean  @default(true)
  version     String?
  settings    String?  // JSON configuration

  // Relations
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  node        NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)

  @@unique([userId, nodeId])
  @@index([userId])
  @@index([nodeId])
}

// Doku subscriptions
model Subscription {
  id                  String    @id @default(uuid())
  userId              String
  dokuSubscriptionId  String?   @unique
  dokuCustomerId      String?
  status              String    // active, canceled, incomplete, past_due, trialing, unpaid
  planId              String
  currentPeriodStart  DateTime
  currentPeriodEnd    DateTime
  cancelAtPeriodEnd   Boolean   @default(false)
  canceledAt          DateTime?
  trialStart          DateTime?
  trialEnd            DateTime?
  metadata            String?   // JSON
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relations
  user                User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([dokuSubscriptionId])
}

// Enhanced Node Installation Registry for Plug & Play System
model InstalledNode {
  id          String   @id @default(uuid())
  userId      String
  nodeId      String
  version     String
  status      String   // installing, installed, failed, updating, uninstalling
  installPath String?  // Path where node code is stored
  config      Json?    // Node-specific configuration
  enabled     Boolean  @default(true)
  installedAt DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastUsed    DateTime?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  node NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)

  @@unique([userId, nodeId])
  @@index([userId])
  @@index([nodeId])
  @@index([status])
}

// Node Execution Logs for Monitoring
model NodeExecutionLog {
  id          String   @id @default(uuid())
  userId      String
  workflowId  String?
  nodeId      String
  nodeType    String
  status      String   // running, completed, failed, cancelled
  input       Json?
  output      Json?
  error       String?
  duration    Int?     // Execution time in milliseconds
  startedAt   DateTime @default(now())
  completedAt DateTime?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  workflow Workflow? @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([workflowId])
  @@index([nodeId])
  @@index([status])
  @@index([startedAt])
}

// Node Code Storage for Dynamic Loading
model NodeCode {
  id          String   @id @default(uuid())
  nodeId      String
  version     String
  code        String   // JavaScript code for the node
  dependencies Json?   // Required dependencies
  permissions Json?    // Required permissions
  checksum    String   // Code integrity verification
  createdAt   DateTime @default(now())

  node NodePlugin @relation(fields: [nodeId], references: [id], onDelete: Cascade)

  @@unique([nodeId, version])
  @@index([nodeId])
  @@index([version])
}

// Workflow Execution Tracking
model WorkflowExecution {
  id            String    @id @default(uuid())
  workflowId    String
  workflow      Workflow  @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  userId        String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  status        String    // running, completed, failed, cancelled, paused
  startTime     DateTime  @default(now())
  endTime       DateTime?
  variables     String?   // JSON string of execution variables
  options       String?   // JSON string of execution options
  results       String?   // JSON string of execution results
  logs          String?   // JSON string of execution logs
  error         String?   // Error message if failed
  triggerNodeId String?   // Node that triggered the execution
  progress      Float     @default(0) // 0-100
  createdAt     DateTime  @default(now())

  @@index([workflowId])
  @@index([userId])
  @@index([status])
  @@index([startTime])
}

// Application Settings Model
model AppSettings {
  id          String   @id @default(uuid())
  category    String   // payments, billing, subscriptions, marketplace, etc.
  key         String   // specific setting key within category
  value       String   // setting value (stored as string, parsed by type)
  type        String   @default("string") // boolean, string, number, array, object
  description String?  // human-readable description
  isSystem    Boolean  @default(false) // system settings vs user-configurable
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?
  updatedBy   String?

  // Relations
  createdByUser User? @relation("SettingsCreatedBy", fields: [createdBy], references: [id], onDelete: SetNull)
  updatedByUser User? @relation("SettingsUpdatedBy", fields: [updatedBy], references: [id], onDelete: SetNull)

  @@unique([category, key])
  @@index([category])
  @@index([isSystem])
}

// Doku Payment Transactions
model Payment {
  id                  String   @id @default(uuid())
  userId              String
  amount              Float
  currency            String   @default("IDR")
  status              String   // pending, processing, completed, failed, cancelled, expired
  paymentMethod       String?
  invoiceNumber       String
  dokuTokenId         String?
  dokuSessionId       String?
  dokuTransactionId   String?
  dokuReferenceNumber String?
  metadata            String?  // JSON
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([invoiceNumber])
  @@index([dokuTokenId])
  @@index([dokuTransactionId])
}

// Revenue Tracking for Node Sales
model Revenue {
  id            String   @id @default(uuid())
  userId        String
  nodeId        String?
  amount        Float
  commission    Float
  transactionId String?
  type          String   // node_sale, subscription, etc.
  createdAt     DateTime @default(now())

  // Relations
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  node          NodePlugin? @relation(fields: [nodeId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([nodeId])
  @@index([type])
}
