"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Filter,
  Star,
  TrendingUp,
  Clock,
  Grid3X3,
  List,
  SlidersHorizontal
} from "lucide-react";
import { NodeCard } from "./node-card";
import { PaymentModal } from "./payment-modal";
import { NodePlugin, MarketplaceFilters, NodeCategory, NodeTier, InstallationStatus } from "@/lib/marketplace/types";
import { marketplaceAPI } from "@/lib/marketplace/api";
import { useInstalledNodes } from "@/hooks/use-installed-nodes";

interface MarketplaceProps {
  onInstallNode?: (nodeId: string) => void;
  onPreviewNode?: (node: NodePlugin) => void;
  onPurchaseNode?: (nodeId: string) => void;
}

export function Marketplace({ onInstallNode, onPreviewNode, onPurchaseNode }: MarketplaceProps) {
  const [nodes, setNodes] = useState<NodePlugin[]>([]);
  const [featuredNodes, setFeaturedNodes] = useState<NodePlugin[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<MarketplaceFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState('all');

  // Payment modal state
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedNodeForPurchase, setSelectedNodeForPurchase] = useState<NodePlugin | null>(null);

  // Use the installed nodes hook
  const {
    installedNodes,
    installNode,
    uninstallNode,
    isNodeInstalled,
    getInstalledNode
  } = useInstalledNodes();

  // Load initial data
  useEffect(() => {
    loadMarketplaceData();
  }, []);

  // Search and filter nodes
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      searchNodes();
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, filters]);

  const loadMarketplaceData = async () => {
    try {
      setLoading(true);
      const [allNodes, featured] = await Promise.all([
        marketplaceAPI.searchNodes({ limit: 20 }),
        marketplaceAPI.getFeaturedNodes()
      ]);

      setNodes(allNodes.nodes);
      setFeaturedNodes(featured);
    } catch (error) {
      console.error('Failed to load marketplace data:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchNodes = async () => {
    try {
      const searchFilters: MarketplaceFilters = {
        ...filters,
        search: searchQuery || undefined,
        limit: 20
      };

      const result = await marketplaceAPI.searchNodes(searchFilters);
      setNodes(result.nodes);
    } catch (error) {
      console.error('Failed to search nodes:', error);
    }
  };

  const handleCategoryFilter = (category: NodeCategory | 'all') => {
    setFilters(prev => ({
      ...prev,
      category: category === 'all' ? undefined : category
    }));
  };

  const handleTierFilter = (tier: NodeTier | 'all') => {
    setFilters(prev => ({
      ...prev,
      tier: tier === 'all' ? undefined : tier
    }));
  };

  const handleSortChange = (sortBy: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: sortBy as any
    }));
  };

  const handlePurchaseNode = (node: NodePlugin) => {
    setSelectedNodeForPurchase(node);
    setPaymentModalOpen(true);
  };

  const handleInstallNode = async (nodeId: string) => {
    try {
      const success = await installNode(nodeId);
      if (success) {
        // Call the parent callback if provided
        if (onInstallNode) {
          onInstallNode(nodeId);
        }
      }
    } catch (error) {
      console.error('Failed to install node:', error);
    }
  };

  const handlePaymentSuccess = (nodeId: string) => {
    // Refresh the marketplace data to reflect the purchase
    loadMarketplaceData();

    // Call the parent callback if provided
    if (onPurchaseNode) {
      onPurchaseNode(nodeId);
    }

    // Optionally trigger installation after purchase
    handleInstallNode(nodeId);
  };

  const getInstallationStatus = (nodeId: string) => {
    const installedNode = getInstalledNode(nodeId);
    if (!installedNode) {
      return undefined; // Not installed
    }

    switch (installedNode.status) {
      case 'installing':
        return InstallationStatus.INSTALLING;
      case 'installed':
        return InstallationStatus.INSTALLED;
      case 'failed':
        return InstallationStatus.ERROR;
      case 'updating':
        return InstallationStatus.UPDATING;
      default:
        return undefined;
    }
  };

  const categories = [
    { id: 'all', label: 'All Categories', count: nodes.length },
    { id: NodeCategory.AI_ML, label: 'AI & ML', count: 0 },
    { id: NodeCategory.DATA_PROCESSING, label: 'Data Processing', count: 0 },
    { id: NodeCategory.API_INTEGRATION, label: 'API Integrations', count: 0 },
    { id: NodeCategory.AUTOMATION, label: 'Automation', count: 0 },
    { id: NodeCategory.UTILITY, label: 'Utilities', count: 0 },
  ];

  const tiers = [
    { id: 'all', label: 'All Tiers' },
    { id: NodeTier.FREE, label: 'Free' },
    { id: NodeTier.PREMIUM, label: 'Premium' },
    { id: NodeTier.ENTERPRISE, label: 'Enterprise' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search nodes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popularity">Popularity</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="price_low">Price: Low to High</SelectItem>
              <SelectItem value="price_high">Price: High to Low</SelectItem>
              <SelectItem value="name">Name</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Category Filters and View Mode Toggle */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Badge
              key={category.id}
              variant={filters.category === category.id || (category.id === 'all' && !filters.category) ? 'default' : 'outline'}
              className="cursor-pointer"
              onClick={() => handleCategoryFilter(category.id as any)}
            >
              {category.label}
              {category.count > 0 && (
                <span className="ml-1 text-xs">({category.count})</span>
              )}
            </Badge>
          ))}
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Separator />

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Grid3X3 className="h-4 w-4" />
            All Nodes
          </TabsTrigger>
          <TabsTrigger value="featured" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Featured
          </TabsTrigger>
          <TabsTrigger value="popular" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Popular
          </TabsTrigger>
          <TabsTrigger value="new" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            New
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {nodes.map((node) => (
              <NodeCard
                key={node.id}
                node={node}
                installationStatus={getInstallationStatus(node.id)}
                onInstall={handleInstallNode}
                onPreview={onPreviewNode}
                onPurchase={() => handlePurchaseNode(node)}
                compact={viewMode === 'list'}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="featured" className="mt-6">
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {featuredNodes.map((node) => (
              <NodeCard
                key={node.id}
                node={node}
                installationStatus={getInstallationStatus(node.id)}
                onInstall={handleInstallNode}
                onPreview={onPreviewNode}
                onPurchase={() => handlePurchaseNode(node)}
                compact={viewMode === 'list'}
              />
            ))}
          </div>
          {featuredNodes.length === 0 && !loading && (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold mb-2">No featured nodes available</h3>
                <p>Check back later for featured workflow nodes</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="popular" className="mt-6">
          <div className="text-center py-8 text-muted-foreground">
            Popular nodes will be loaded here
          </div>
        </TabsContent>

        <TabsContent value="new" className="mt-6">
          <div className="text-center py-8 text-muted-foreground">
            New nodes will be loaded here
          </div>
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {nodes.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No nodes found</h3>
            <p>Try adjusting your search criteria or browse different categories</p>
          </div>
          <Button onClick={() => {
            setSearchQuery('');
            setFilters({});
          }}>
            Clear Filters
          </Button>
        </div>
      )}

      {/* Payment Modal */}
      {selectedNodeForPurchase && (
        <PaymentModal
          isOpen={paymentModalOpen}
          onClose={() => {
            setPaymentModalOpen(false);
            setSelectedNodeForPurchase(null);
          }}
          node={selectedNodeForPurchase}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}
